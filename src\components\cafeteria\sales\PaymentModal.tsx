'use client';

import { useState } from 'react';
import {
  CreditCard,
  Wallet,
  ArrowRightLeft,
  Star,
  CheckCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { currencyFormat } from '@/lib/utils';




interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  totalPrice: number;
  saleType: 'general' | 'staff';
  selectedPaymentMethod: string | null;
  setSelectedPaymentMethod: (method: string | null) => void;
  cart: Array<{
    id: number;
    name: string;
    price: number;
    quantity: number;
  }>;
  onPaymentSuccess: () => void;
}

export default function PaymentModal({
  open,
  onOpenChange,
  totalPrice,
  saleType,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  cart,
  onPaymentSuccess,
}: PaymentModalProps) {
  const [staffId, setStaffId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [canPay, setCanPay] = useState({ wallet: false, creditLimit: false });
  const [isStaffVerified, setIsStaffVerified] = useState(false);

  const handlePayModal = () => {
    onOpenChange(false);
    setIsStaffVerified(false)
    setStaffId('')
  }


  const verifyStaff = async () => {
    if (!staffId) {
      toast.error("Enter Staff ID");
      return;
    };
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/orders/check-payment', {
        code: staffId,
        amount: Number(totalPrice),
      });
      const data = res.data.data;
      setIsLoading(false);
      if (res.status === 200) {
        setCanPay(data);
        setIsStaffVerified(true)
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handlePaymentComplete = async () => {
    if (!selectedPaymentMethod) {
      toast.error("Please select a payment method");
      return;
    }

    try {
      setIsLoading(true);

      // Prepare the order payload
      const orderPayload = {
        items: cart.map(item => ({
          menuId: item.id,
          quantity: item.quantity,
          price: item.price
        })),
        totalAmount: totalPrice,
        paymentMethod: selectedPaymentMethod,
        saleType: saleType,
        ...(saleType === 'staff' && staffId && { staffId: staffId })
      };

      console.log('Order payload:', orderPayload);

      // TODO: Replace with actual order creation API endpoint
      // const res = await myApi.post('/cafeteria/orders/create', orderPayload);

      // For now, simulate successful order creation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsLoading(false);
      toast.success("Order completed successfully!");

      // Reset modal state and call success callback
      onPaymentSuccess();
      onOpenChange(false);
      setIsStaffVerified(false);
      setStaffId('');

    } catch (error) {
      setIsLoading(false);
      toast.error("Failed to complete order. Please try again.");
    }
  };

  return (
    <Modal
      open={open}
      setOpen={onOpenChange}
      title="Select Payment Method"
      description=""
      onSubmit={undefined}
      isLoading={false}
      size="sm"
    >
      <div className="space-y-6">
        {saleType === 'staff' && !isStaffVerified ? (
          <div className="flex flex-col gap-4 py-4">
            <p className="text-sm text-gray-500">
              Please verify staff ID to continue
            </p>
            <div className="flex gap-2">
              <Input
                placeholder="Enter Staff ID"
                value={staffId}
                onChange={(e) => setStaffId(e.target.value)}
                className="flex-1"
              />
              <Button onClick={verifyStaff} disabled={isLoading}>
               {isLoading ? 'Please wait...' : ' Verify'}
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button
              variant={
                selectedPaymentMethod === 'transfer' ? 'default' : 'outline'
              }
              className="flex flex-col items-center justify-center h-24 gap-2"
              onClick={() => setSelectedPaymentMethod('transfer')}
            >
              <ArrowRightLeft className="h-8 w-8" />
              <span>Transfer</span>
            </Button>
            <Button
              variant={selectedPaymentMethod === 'card' ? 'default' : 'outline'}
              className="flex flex-col items-center justify-center h-24 gap-2"
              onClick={() => setSelectedPaymentMethod('card')}
            >
              <CreditCard className="h-8 w-8" />
              <span>Card</span>
            </Button>
            {saleType === 'staff' && (
              <>
                {canPay.wallet && (
                  <Button
                    variant={
                      selectedPaymentMethod === 'wallet' ? 'default' : 'outline'
                    }
                    className="flex flex-col items-center justify-center h-24 gap-2"
                    onClick={() => setSelectedPaymentMethod('wallet')}
                  >
                    <Wallet className="h-8 w-8" />
                    <span>Wallet</span>
                  </Button>
                )}
                {canPay.creditLimit && (
                  <Button
                    variant={
                      selectedPaymentMethod === 'credit' ? 'default' : 'outline'
                    }
                    className="flex flex-col items-center justify-center h-24 gap-2"
                    onClick={() => setSelectedPaymentMethod('credit')}
                  >
                    <Star className="h-8 w-8" />
                    <span>Credit</span>
                  </Button>
                )}
              </>
            )}
          </div>
        )}

        <div className="flex justify-between items-center py-2 border-t border-b">
          <span className="font-bold">Total:</span>
          <span className="font-bold text-lg">₦{currencyFormat(totalPrice)}</span>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handlePayModal}>
            Cancel
          </Button>
          {(saleType !== 'staff' || isStaffVerified) && (
            <Button
              disabled={!selectedPaymentMethod || isLoading}
              onClick={handlePaymentComplete}
            >
              {isLoading ? 'Processing...' : 'Complete Payment'}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
