'use client';

import { useState } from 'react';
import {
  CreditCard,
  Wallet,
  ArrowRightLeft,
  Star,
  CheckCircle,
  Store,
  ShoppingBag,
  Truck,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { currencyFormat } from '@/lib/utils';




interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  totalPrice: number;
  saleType: 'general' | 'staff';
  selectedPaymentMethod: string | null;
  setSelectedPaymentMethod: (method: string | null) => void;
  cart: Array<{
    id: number;
    name: string;
    price: number;
    quantity: number;
  }>;
  onPaymentSuccess: () => void;
}

export default function PaymentModal({
  open,
  onOpenChange,
  totalPrice,
  saleType,
  selectedPaymentMethod,
  setSelectedPaymentMethod,
  cart,
  onPaymentSuccess,
}: PaymentModalProps) {
  const [staffId, setStaffId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [canPay, setCanPay] = useState({ wallet: false, creditLimit: false });
  const [isStaffVerified, setIsStaffVerified] = useState(false);
  const [orderType, setOrderType] = useState<'DINE_IN' | 'TAKEAWAY' | 'DELIVERY' | null>(null);
  const [currentStep, setCurrentStep] = useState<'orderType' | 'staffVerification' | 'payment'>('orderType');

  const handlePayModal = () => {
    onOpenChange(false);
    setIsStaffVerified(false);
    setStaffId('');
    setOrderType(null);
    setCurrentStep('orderType');
  };

  const handleOrderTypeSelect = (type: 'DINE_IN' | 'TAKEAWAY' | 'DELIVERY') => {
    setOrderType(type);
    if (saleType === 'staff') {
      setCurrentStep('staffVerification');
    } else {
      setCurrentStep('payment');
    }
  };

  const handleStaffVerified = () => {
    setIsStaffVerified(true);
    setCurrentStep('payment');
  };


  const verifyStaff = async () => {
    if (!staffId) {
      toast.error("Enter Staff ID");
      return;
    };
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/orders/check-payment', {
        code: staffId,
        amount: Number(totalPrice),
      });
      const data = res.data.data;
      setIsLoading(false);
      if (res.status === 200) {
        setCanPay(data);
        handleStaffVerified();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handlePaymentComplete = async () => {
    if (!selectedPaymentMethod) {
      toast.error("Please select a payment method");
      return;
    }

    if (!orderType) {
      toast.error("Please select an order type");
      return;
    }

    try {
      setIsLoading(true);

      // Prepare the order payload
      const orderPayload = {
        cart: cart,
        totalAmount: totalPrice,
        paymentMethod: selectedPaymentMethod,
        saleType: saleType,
        orderType: orderType,
        ...(saleType === 'staff' && staffId && { staffId: staffId })
      };

      console.log('Order payload:', orderPayload);

      // TODO: Replace with actual order creation API endpoint
      // const res = await myApi.post('/cafeteria/orders/create', orderPayload);

      // For now, simulate successful order creation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsLoading(false);
      toast.success("Order completed successfully!");

      // Reset modal state and call success callback
      onPaymentSuccess();
      onOpenChange(false);
      setIsStaffVerified(false);
      setStaffId('');
      setOrderType(null);
      setCurrentStep('orderType');

    } catch (error) {
      setIsLoading(false);
      toast.error("Failed to complete order. Please try again.");
    }
  };

  const getModalTitle = () => {
    switch (currentStep) {
      case 'orderType':
        return 'Select Order Type';
      case 'staffVerification':
        return 'Staff Verification';
      case 'payment':
        return 'Select Payment Method';
      default:
        return 'Complete Order';
    }
  };

  return (
    <Modal
      open={open}
      setOpen={onOpenChange}
      title={getModalTitle()}
      description=""
      onSubmit={undefined}
      isLoading={false}
      size="sm"
    >
      <div className="space-y-6">
        {currentStep === 'orderType' && (
          <div className="grid grid-cols-1 gap-4 py-4">
            <p className="text-sm text-gray-500 text-center mb-2">
              Please select how you would like to receive your order
            </p>
            <div className="grid grid-cols-1 gap-3">
              <Button
                variant={orderType === 'DINE_IN' ? 'default' : 'outline'}
                className="flex items-center justify-center h-16 gap-3"
                onClick={() => handleOrderTypeSelect('DINE_IN')}
              >
                <Store className="h-6 w-6" />
                <div className="text-left">
                  <div className="font-medium">Dine In</div>
                  <div className="text-xs text-muted-foreground">Eat at the restaurant</div>
                </div>
              </Button>
              <Button
                variant={orderType === 'TAKEAWAY' ? 'default' : 'outline'}
                className="flex items-center justify-center h-16 gap-3"
                onClick={() => handleOrderTypeSelect('TAKEAWAY')}
              >
                <ShoppingBag className="h-6 w-6" />
                <div className="text-left">
                  <div className="font-medium">Takeaway</div>
                  <div className="text-xs text-muted-foreground">Pick up your order</div>
                </div>
              </Button>
              <Button
                variant={orderType === 'DELIVERY' ? 'default' : 'outline'}
                className="flex items-center justify-center h-16 gap-3"
                onClick={() => handleOrderTypeSelect('DELIVERY')}
              >
                <Truck className="h-6 w-6" />
                <div className="text-left">
                  <div className="font-medium">Delivery</div>
                  <div className="text-xs text-muted-foreground">Deliver to your location</div>
                </div>
              </Button>
            </div>
          </div>
        )}

        {currentStep === 'staffVerification' && (
          <div className="flex flex-col gap-4 py-4">
            <p className="text-sm text-gray-500">
              Please verify staff ID to continue
            </p>
            <div className="flex gap-2">
              <Input
                placeholder="Enter Staff ID"
                value={staffId}
                onChange={(e) => setStaffId(e.target.value)}
                className="flex-1"
              />
              <Button onClick={verifyStaff} disabled={isLoading}>
               {isLoading ? 'Please wait...' : ' Verify'}
              </Button>
            </div>
          </div>
        )}

        {currentStep === 'payment' && (
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button
              variant={
                selectedPaymentMethod === 'transfer' ? 'default' : 'outline'
              }
              className="flex flex-col items-center justify-center h-24 gap-2"
              onClick={() => setSelectedPaymentMethod('transfer')}
            >
              <ArrowRightLeft className="h-8 w-8" />
              <span>Transfer</span>
            </Button>
            <Button
              variant={selectedPaymentMethod === 'card' ? 'default' : 'outline'}
              className="flex flex-col items-center justify-center h-24 gap-2"
              onClick={() => setSelectedPaymentMethod('card')}
            >
              <CreditCard className="h-8 w-8" />
              <span>Card</span>
            </Button>
            {saleType === 'staff' && (
              <>
                {canPay.wallet && (
                  <Button
                    variant={
                      selectedPaymentMethod === 'wallet' ? 'default' : 'outline'
                    }
                    className="flex flex-col items-center justify-center h-24 gap-2"
                    onClick={() => setSelectedPaymentMethod('wallet')}
                  >
                    <Wallet className="h-8 w-8" />
                    <span>Wallet</span>
                  </Button>
                )}
                {canPay.creditLimit && (
                  <Button
                    variant={
                      selectedPaymentMethod === 'credit' ? 'default' : 'outline'
                    }
                    className="flex flex-col items-center justify-center h-24 gap-2"
                    onClick={() => setSelectedPaymentMethod('credit')}
                  >
                    <Star className="h-8 w-8" />
                    <span>Credit</span>
                  </Button>
                )}
              </>
            )}
          </div>
        )}

        <div className="flex justify-between items-center py-2 border-t border-b">
          <span className="font-bold">Total:</span>
          <span className="font-bold text-lg">₦{currencyFormat(totalPrice)}</span>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handlePayModal}>
            Cancel
          </Button>
          {(saleType !== 'staff' || isStaffVerified) && (
            <Button
              disabled={!selectedPaymentMethod || isLoading}
              onClick={handlePaymentComplete}
            >
              {isLoading ? 'Processing...' : 'Complete Payment'}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
